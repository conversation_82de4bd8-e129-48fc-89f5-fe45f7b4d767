import {
  _CONTACT_CHANNEL_OPTIONS,
  _FOLLOW_UP_STATUS_OPTIONS,
  _OPPORTUNITY_OPTIONS,
  _SERVICE_OPTIONS,
} from "@components/badge";
import { Button, Input, Select, Textarea } from "@components/common";
import { useForm } from "@tanstack/react-form";
import { useTranslation } from "react-i18next";
import type { FormField, FormValues } from "./interface";

export const AddLeadForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const formFields: FormField[] = [
    {
      colSpan: 2,
      gridCols: 3,
      id: "name",
      label: t("addLead.name"),
      placeholder: t("addLead.name"),
      required: true,
      type: "input",
      variant: "transparent",
    },
    {
      colSpan: 1,
      gridCols: 2,
      id: "opportunity",
      label: t("addLead.opportunity"),
      options: _OPPORTUNITY_OPTIONS,
      placeholder: t("addLead.opportunity"),
      type: "select",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "followUpStatus",
      label: t("addLead.followUpStatus"),
      options: _FOLLOW_UP_STATUS_OPTIONS,
      placeholder: t("addLead.followUpStatus"),
      type: "select",
    },
    {
      colSpan: 1,
      gridCols: 2,
      id: "contactChannel",
      label: t("addLead.contactChannel"),
      options: _CONTACT_CHANNEL_OPTIONS,
      placeholder: t("addLead.contactChannel"),
      required: true,
      type: "select",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "servicesOfInterest",
      label: t("addLead.servicesOfInterest"),
      options: _SERVICE_OPTIONS,
      placeholder: t("addLead.servicesOfInterest"),
      type: "select",
    },
    {
      colSpan: 1,
      defaultValue: today,
      gridCols: 2,
      id: "startDate",
      label: t("addLead.startDate"),
      type: "date",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "contactInfo",
      label: t("addLead.contactInfo"),
      placeholder: t("addLead.contactInfo"),
      type: "input",
      variant: "transparent",
    },
    {
      colSpan: 1,
      disabled: true,
      gridCols: 2,
      id: "followUpDate",
      label: t("addLead.followUpDate"),
      type: "input",
    },
  ];

  const noteField: FormField = {
    className: "w-full flex-1 resize-none",
    id: "note",
    label: t("addLead.note"),
    placeholder: t("addLead.note"),
    type: "textarea",
  };

  const renderField = (field: FormField) => {
    const validators = field.required
      ? {
          onChange: ({ value }: { value: string }) =>
            !value ? `${field.label} is required` : undefined,
        }
      : {};

    return (
      <form.Field key={field.id} name={field.id as keyof FormValues} validators={validators}>
        {(fieldApi) => (
          <div className={`group col-span-${field.colSpan} grid grid-cols-${field.gridCols}`}>
            <label htmlFor={fieldApi.name} className="flex gap-1 text-h6">
              <span className="text-h6">{field.label}</span>
              {field.required && <span className="text-error text-h6">*</span>}
            </label>

            <div className={field.gridCols === 3 ? "col-span-2" : ""}>
              {field.type === "input" && (
                <Input
                  id={fieldApi.name}
                  type={field.inputType || "text"}
                  placeholder={field.placeholder}
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  disabled={field.disabled}
                  className={`${field.gridCols === 3 ? "col-span-2 " : ""}flex-1 ${
                    field.variant === "transparent" ? "group-hover:bg-base-200 " : ""
                  }${fieldApi.state.meta.errors.length > 0 ? "border-error" : ""}`}
                  variant={field.variant || "default"}
                />
              )}

              {field.type === "date" && (
                <Input
                  id={fieldApi.name}
                  type="date"
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  disabled={field.disabled}
                  className="w-full"
                />
              )}

              {field.type === "select" && (
                <Select
                  id={fieldApi.name}
                  options={field.options}
                  size="sm"
                  variant="popup"
                  value={fieldApi.state.value}
                  onChange={(value) => fieldApi.handleChange(value)}
                  className={`${field.gridCols === 3 ? "flex-1 " : ""}${
                    fieldApi.state.meta.errors.length > 0 ? "border-error" : ""
                  }`}
                  placeholder={field.placeholder}
                />
              )}

              {field.type === "textarea" && (
                <Textarea
                  id={fieldApi.name}
                  placeholder={field.placeholder}
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  className={field.className || ""}
                />
              )}

              {fieldApi.state.meta.errors.length > 0 && (
                <div
                  className={`text-error text-sm ${field.gridCols === 3 ? "col-span-full" : ""}`}
                >
                  {fieldApi.state.meta.errors[0]}
                </div>
              )}
            </div>
          </div>
        )}
      </form.Field>
    );
  };

  const renderNoteField = (field: FormField) => {
    return (
      <form.Field key={field.id} name={field.id as keyof FormValues}>
        {(fieldApi) => (
          <div className="flex min-h-0 flex-1 flex-col gap-2">
            <label htmlFor={fieldApi.name} className="text-h6">
              {field.label}
            </label>
            <Textarea
              className={field.className}
              id={fieldApi.name}
              placeholder={field.placeholder}
              value={fieldApi.state.value}
              onChange={(e) => fieldApi.handleChange(e.target.value)}
              onBlur={fieldApi.handleBlur}
            />
            {fieldApi.state.meta.errors.length > 0 && (
              <div className="text-error text-sm">{fieldApi.state.meta.errors[0]}</div>
            )}
          </div>
        )}
      </form.Field>
    );
  };

  const form = useForm({
    defaultValues: {
      contactChannel: "",
      contactInfo: "",
      followUpDate: "",
      followUpStatus: "",
      name: "",
      note: "",
      opportunity: "",
      servicesOfInterest: "",
      startDate: today,
    },
    onSubmit: async ({ value }: { value: FormValues }) => {
      alert(JSON.stringify(value, null, 2));
    },
  });

  return (
    <form
      className="flex h-full flex-col gap-6 overflow-hidden p-1"
      onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          {formFields.map((field) => renderField(field))}
        </div>
      </div>

      {/* Note Field */}
      {renderNoteField(noteField)}

      {/* Debug Info */}
      <form.Subscribe selector={(state) => [state.values, state.errors, state.canSubmit]}>
        {([values, errors, canSubmit]) => (
          <div className="rounded bg-gray-100 p-2 text-gray-500 text-xs">
            <div>Can Submit: {canSubmit ? "Yes" : "No"}</div>
            <div>Errors: {JSON.stringify(errors)}</div>
            <div>Values: {JSON.stringify(values)}</div>
          </div>
        )}
      </form.Subscribe>

      {/* Buttons */}
      <div className="flex w-full justify-end gap-3 pt-4">
        <Button variant="outline" className="w-28" type="button">
          {t("common.draft")}
        </Button>
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
          {([canSubmit, isSubmitting]) => (
            <Button className="w-28" type="submit" disabled={!canSubmit || isSubmitting}>
              {isSubmitting ? t("common.loading") : t("common.save")}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  );
};
