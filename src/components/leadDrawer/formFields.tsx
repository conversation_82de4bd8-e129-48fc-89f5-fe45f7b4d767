import { _CONTACT_CHANNEL_OPTIONS, _FOLLOW_UP_STATUS_OPTIONS, _OPPORTUNITY_OPTIONS, _SERVICE_OPTIONS } from "@components/badge";
import { useTranslation } from "react-i18next";
import type { FormField } from "./interface";

export const AddLeadFormField = () => {
     const {t} = useTranslation();

     const today = new Date().toISOString().split("T")[0];

       const formFields: FormField[] = [
         {
           colSpan: 2,
           gridCols: 3,
           id: "name",
           label: t("addLead.name"),
           placeholder: t("addLead.name"),
           required: true,
           type: "input",
           variant: "transparent",
         },
         {
           colSpan: 1,
           gridCols: 2,
           id: "opportunity",
           label: t("addLead.opportunity"),
           options: _OPPORTUNITY_OPTIONS,
           placeholder: t("addLead.opportunity"),
           type: "select",
         },
         {
           colSpan: 2,
           gridCols: 3,
           id: "followUpStatus",
           label: t("addLead.followUpStatus"),
           options: _FOLLOW_UP_STATUS_OPTIONS,
           placeholder: t("addLead.followUpStatus"),
           type: "select",
         },
         {
           colSpan: 1,
           gridCols: 2,
           id: "contactChannel",
           label: t("addLead.contactChannel"),
           options: _CONTACT_CHANNEL_OPTIONS,
           placeholder: t("addLead.contactChannel"),
           required: true,
           type: "select",
         },
         {
           colSpan: 2,
           gridCols: 3,
           id: "servicesOfInterest",
           label: t("addLead.servicesOfInterest"),
           options: _SERVICE_OPTIONS,
           placeholder: t("addLead.servicesOfInterest"),
           type: "select",
         },
         {
           colSpan: 1,
           defaultValue: today,
           gridCols: 2,
           id: "startDate",
           label: t("addLead.startDate"),
           type: "date",
         },
         {
           colSpan: 2,
           gridCols: 3,
           id: "contactInfo",
           label: t("addLead.contactInfo"),
           placeholder: t("addLead.contactInfo"),
           type: "input",
           variant: "transparent",
         },
         {
           colSpan: 1,
           disabled: true,
           gridCols: 2,
           id: "followUpDate",
           label: t("addLead.followUpDate"),
           type: "input",
         },
       ];

  return (

};
